import express, { NextFunction, Request, Response } from 'express';
import { catchAsync, getPolyClient } from '@src/utils';

const router = express.Router();

router.get('/', catchAsync(async (req: Request, res: Response) => {
  const slug = req.query.slug as (string | undefined);

  if (!slug) {
    res.status(400).send({ error: 'Missing slug' });
    return;
  }

  //Get data for event
  const client = getPolyClient();
  const eventData = await client.getEvent(slug);

  if (!eventData) {
    res.status(500).send({ error: 'Event not found' });
    return;
  }

  res.send(eventData);
}));

router.get('/markets', catchAsync(async (req: Request, res: Response) => {
  const {
    limit,
    offset,
    order,
    ascending,
    id,
    slug,
    archived,
    active,
    closed,
    clob_token_ids,
    condition_ids,
    liquidity_num_min,
    liquidity_num_max,
    volume_num_min,
    volume_num_max,
    start_date_min,
    start_date_max,
    end_date_min,
    end_date_max,
    tag_id,
    related_tags
  } = req.query;

  //Build request object
  const request: any = {};

  //Handle simple parameters
  if (limit) request.limit = Number(limit);
  if (offset) request.offset = Number(offset);
  if (order) request.order = order as string;
  if (ascending) request.ascending = ascending === "true";
  if (archived) request.archived = archived === "true";
  if (active) request.active = active === "true";
  if (closed) request.closed = closed === "true";
  if (liquidity_num_min) request.liquidity_num_min = Number(liquidity_num_min);
  if (liquidity_num_max) request.liquidity_num_max = Number(liquidity_num_max);
  if (volume_num_min) request.volume_num_min = Number(volume_num_min);
  if (volume_num_max) request.volume_num_max = Number(volume_num_max);
  if (start_date_min) request.start_date_min = start_date_min as string;
  if (start_date_max) request.start_date_max = start_date_max as string;
  if (end_date_min) request.end_date_min = end_date_min as string;
  if (end_date_max) request.end_date_max = end_date_max as string;
  if (tag_id) request.tag_id = Number(tag_id);
  if (related_tags) request.related_tags = related_tags === "true";

  //Handle array parameters (can be passed multiple times or as comma-separated)
  if (id) {
    request.id = Array.isArray(id) ? id.map(Number) : [Number(id)];
  }

  if (slug) {
    request.slug = Array.isArray(slug) ? slug as string[] : [slug as string];
  }

  if (clob_token_ids) {
    if (Array.isArray(clob_token_ids)) {
      request.clob_token_ids = clob_token_ids as string[];
    }
    else {
      //Handle comma-separated values
      request.clob_token_ids = (clob_token_ids as string).split(",");
    }
  }

  if (condition_ids) {
    if (Array.isArray(condition_ids)) {
      request.condition_ids = condition_ids as string[];
    }
    else {
      //Handle comma-separated values
      request.condition_ids = (condition_ids as string).split(",");
    }
  }

  const client = getPolyClient();
  const marketsData = await client.getMarkets(request);

  res.send(marketsData);
}));

router.get('/market/orderbook', catchAsync(async (req: Request, res: Response) => {
  const { idA, idB } = req.query as Record<string, string | undefined>;

  if (!idA || !idB) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }
  const client = getPolyClient();
  const resData = await client.getBook(idA, idB);

  //console.log(resData);
  res.send(resData);
}));

router.get('/market/price-history', catchAsync(async (req: Request, res: Response) => {
  const { assetId, interval } = req.query as Record<string, string | undefined>;

  if (!assetId || !interval) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }
  const client = getPolyClient();
  const resData = await client.getPriceHistory(assetId, interval as any);

  res.send(resData);
}));

router.get('/market/history', catchAsync(async (req: Request, res: Response) => {
  const { market, interval } = req.query as Record<string, string | undefined>;

  if (!market || !interval || !Number(interval)) {
    console.log(req.query);
    res.status(400).send({ error: 'Missing/invalid required parameters' });
    return;
  }

  const marketArr = market.split(",");
  const client = getPolyClient();
  await global.marketHistoryModel.updateRecentMarketHistory(client, marketArr);
  const resData = await global.marketHistoryModel.getRecentMarketHistory(marketArr, Number(interval))

  res.send(resData);
}));

router.get('/elon-tweets', catchAsync(async (req: Request, res: Response) => {
  const { eventId } = req.query as Record<string, string | undefined>;

  if (!eventId) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const resData = await client.getElonTweets(eventId);

  //Check if tweet count has changed since last check
  if (global.appData.lastElonTweetCount != resData.tweetCount) {
    global.appData.lastElonTweetCount = resData.tweetCount;
    global.appData.lastElonTweetTime = new Date();

    //Update db
    global.appDataModel.updateAppData('lastElonTweetCount', resData.tweetCount.toString());
    global.appDataModel.updateAppData('lastElonTweetTime', global.appData.lastElonTweetTime.toISOString());
  }

  resData.tweetCount = global.appData.lastElonTweetCount;
  resData.lastTweetTime = global.appData.lastElonTweetTime.toISOString();

  res.send(resData);
}));

router.get('/quick-search', catchAsync(async (req: Request, res: Response) => {
  const { q, status } = req.query as Record<string, string | undefined>;

  if (!q || !status) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const resData = await client.quickSearchEvents(q, status as any);

  res.send(resData);
}));

router.get('/search', catchAsync(async (req: Request, res: Response) => {
  const { q, status, page, sort, cat, freq, noSports } = req.query as Record<string, string | undefined>;

  if (!q || !status || !page) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const resData = await client.searchEvents(q, Number(page), status as any, sort as any, freq as any, cat, noSports == "true");

  res.send(resData);
}));

export default router;

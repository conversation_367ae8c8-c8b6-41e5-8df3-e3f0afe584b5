import express, { NextFunction, Request, Response } from 'express';
import { catchAsync, getPolyClient } from '@src/utils';

const router = express.Router();

router.get('/', catchAsync(async (req: Request, res: Response) => {
  const slug = req.query.slug as (string | undefined);

  if (!slug) {
    res.status(400).send({ error: 'Missing slug' });
    return;
  }

  //Get data for event
  const client = getPolyClient();
  const eventData = await client.getEvent(slug);

  if (!eventData) {
    res.status(500).send({ error: 'Event not found' });
    return;
  }

  res.send(eventData);
}));

router.get('/markets', catchAsync(async (req: Request, res: Response) => {
  const { condition_ids } = req.query;

  if (!condition_ids) {
    res.status(400).send({ error: 'Missing condition_ids parameter' });
    return;
  }

  //Handle condition_ids as array or comma-separated string
  let conditionIdsArray: string[];
  if (Array.isArray(condition_ids)) {
    conditionIdsArray = condition_ids as string[];
  }
  else {
    conditionIdsArray = (condition_ids as string).split(",");
  }

  const client = getPolyClient();
  const marketsData = await client.getMarkets(conditionIdsArray);

  res.send(marketsData);
}));

router.get('/market/orderbook', catchAsync(async (req: Request, res: Response) => {
  const { idA, idB } = req.query as Record<string, string | undefined>;

  if (!idA || !idB) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }
  const client = getPolyClient();
  const resData = await client.getBook(idA, idB);

  //console.log(resData);
  res.send(resData);
}));

router.get('/market/price-history', catchAsync(async (req: Request, res: Response) => {
  const { assetId, interval } = req.query as Record<string, string | undefined>;

  if (!assetId || !interval) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }
  const client = getPolyClient();
  const resData = await client.getPriceHistory(assetId, interval as any);

  res.send(resData);
}));

router.get('/market/history', catchAsync(async (req: Request, res: Response) => {
  const { market, interval } = req.query as Record<string, string | undefined>;

  if (!market || !interval || !Number(interval)) {
    console.log(req.query);
    res.status(400).send({ error: 'Missing/invalid required parameters' });
    return;
  }

  const marketArr = market.split(",");
  const client = getPolyClient();
  await global.marketHistoryModel.updateRecentMarketHistory(client, marketArr);
  const resData = await global.marketHistoryModel.getRecentMarketHistory(marketArr, Number(interval))

  res.send(resData);
}));

router.get('/elon-tweets', catchAsync(async (req: Request, res: Response) => {
  const { eventId } = req.query as Record<string, string | undefined>;

  if (!eventId) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const resData = await client.getElonTweets(eventId);

  //Check if tweet count has changed since last check
  if (global.appData.lastElonTweetCount != resData.tweetCount) {
    global.appData.lastElonTweetCount = resData.tweetCount;
    global.appData.lastElonTweetTime = new Date();

    //Update db
    global.appDataModel.updateAppData('lastElonTweetCount', resData.tweetCount.toString());
    global.appDataModel.updateAppData('lastElonTweetTime', global.appData.lastElonTweetTime.toISOString());
  }

  resData.tweetCount = global.appData.lastElonTweetCount;
  resData.lastTweetTime = global.appData.lastElonTweetTime.toISOString();

  res.send(resData);
}));

router.get('/quick-search', catchAsync(async (req: Request, res: Response) => {
  const { q, status } = req.query as Record<string, string | undefined>;

  if (!q || !status) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const resData = await client.quickSearchEvents(q, status as any);

  res.send(resData);
}));

router.get('/search', catchAsync(async (req: Request, res: Response) => {
  const { q, status, page, sort, cat, freq, noSports } = req.query as Record<string, string | undefined>;

  if (!q || !status || !page) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const resData = await client.searchEvents(q, Number(page), status as any, sort as any, freq as any, cat, noSports == "true");

  res.send(resData);
}));

export default router;
